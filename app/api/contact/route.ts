import { getDb, dbSchema } from '@/lib/db';
import { eq, like, or, and, count } from 'drizzle-orm';
import { getAuth } from '@/lib/auth/config';
import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';



/**
 * Build search conditions for contacts based on search term
 * Searches across phone and name fields
 */
function buildSearchConditions(search: string) {
  if (!search.trim()) {
    return undefined;
  }

  const searchTerm = `%${search.trim()}%`;
  return or(
    like(dbSchema.contacts.phone, searchTerm),
    like(dbSchema.contacts.name, searchTerm)
  );
}

export async function GET(request: NextRequest) {
  try {
    // Authentication
    const auth = await getAuth();
    const requestHeaders = await headers();
    const session = await auth.api.getSession({
      headers: requestHeaders,
    });

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = {
      ...session.user,
      role: (session.user as { role?: string }).role || 'user',
    };

    const db = await getDb();
    const searchParams = request.nextUrl.searchParams;

    // Parse and validate pagination parameters
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
    const perPage = Math.min(100, Math.max(1, parseInt(searchParams.get('perPage') || '20')));
    const search = searchParams.get('search') || '';
    const offset = (page - 1) * perPage;

    // Build search conditions
    const searchConditions = buildSearchConditions(search);

    // Access role property (better-auth additional field)
    const userRole = user.role || 'user';

    if (userRole === 'admin') {
      // Admin can see all contacts with search
      const whereClause = searchConditions;

      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(dbSchema.contacts)
        .where(whereClause);

      const total = totalResult[0]?.count || 0;

      // Get paginated contacts
      const contacts = await db
        .select()
        .from(dbSchema.contacts)
        .where(whereClause)
        .orderBy(dbSchema.contacts.name)
        .limit(perPage)
        .offset(offset);

      return NextResponse.json({
        contacts,
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage),
      });
    } else {
      // Regular users can only see their own contacts with search
      const userFilter = eq(dbSchema.contacts.userId, user.id);
      const whereClause = searchConditions
        ? and(userFilter, searchConditions)
        : userFilter;

      // Get total count
      const totalResult = await db
        .select({ count: count() })
        .from(dbSchema.contacts)
        .where(whereClause);

      const total = totalResult[0]?.count || 0;

      // Get paginated contacts
      const contacts = await db
        .select()
        .from(dbSchema.contacts)
        .where(whereClause)
        .orderBy(dbSchema.contacts.name)
        .limit(perPage)
        .offset(offset);

      return NextResponse.json({
        contacts,
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage),
      });
    }
  } catch (error) {
    console.error('Error fetching contacts:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}