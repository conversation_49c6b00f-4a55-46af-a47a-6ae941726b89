import { NextRequest, NextResponse } from 'next/server';
import { getDb, contacts, phonebookKeyPasswords, user } from '@/lib/db';
import { eq, and } from 'drizzle-orm';
import { logApiAccess } from '@/lib/logging';

/**
 * Yealink IP Phone Directory XML API Endpoint
 * 
 * Security Requirements:
 * - User-Agent must contain "Yealink"
 * - Requires 'key' and 'secret' query parameters
 * - Validates key/secret against phonebookKeyPasswords table
 * - Input sanitization to prevent injection attacks
 * - GDPR compliant logging
 */

interface YealinkContact {
  name: string;
  telephone: string;
}

/**
 * Sanitize input to prevent XML injection and other attacks
 */
function sanitizeXmlValue(value: string): string {
  if (!value) return '';

  return value
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
    .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
    .substring(0, 255); // Limit length
}

/**
 * Validate phone number format for Yealink compatibility
 * Ensures phone numbers meet minimum requirements and contain valid characters
 */
function isValidPhoneNumber(phone: string): boolean {
  if (!phone || typeof phone !== 'string') return false;

  // Remove whitespace for validation
  const trimmed = phone.trim();
  if (!trimmed) return false;

  // Check for obviously invalid patterns
  if (trimmed.match(/^\+{2,}/)) return false; // Multiple + signs at start

  // Check for letters - allow only if they are extension markers
  const hasLetters = trimmed.match(/[a-zA-Z]/g);
  if (hasLetters) {
    // Allow 'x', 'ext', or 'extension' followed by digits
    if (!trimmed.match(/\s*(x|ext|extension)\s*\d+$/i)) {
      return false;
    }
  }

  // Extract digits only for counting
  const digitsOnly = trimmed.replace(/[^0-9]/g, '');

  // Must have between 7 and 15 digits (E.164 standard)
  if (digitsOnly.length < 7 || digitsOnly.length > 15) return false;

  // Must contain at least some digits
  if (!/\d/.test(trimmed)) return false;

  return true;
}

/**
 * Format phone number for Yealink directory compatibility
 * Converts international numbers to local format
 * Returns a clean format suitable for VoIP dialing or null if invalid
 */
function formatPhoneNumberForYealink(phone: string): string | null {
  if (!isValidPhoneNumber(phone)) return null;

  const trimmed = phone.trim();

  // Handle extensions - extract main number and extension separately
  const extensionMatch = trimmed.match(/^(.+?)(?:\s*(?:x|ext|extension)\s*(\d+))$/i);
  const mainNumber = extensionMatch ? extensionMatch[1].trim() : trimmed;
  const extension = extensionMatch ? extensionMatch[2] : null;

  // Clean the main number first - keep only digits, remove all + signs
  let cleaned = mainNumber.replace(/[^\d]/g, '');

  // Handle international numbers by country code
  // Germany (49) -> remove 49 and add leading 0 for local format
  if (cleaned.startsWith('49') && cleaned.length > 10) {
    cleaned = '0' + cleaned.substring(2);
  }
  // Add more country code handling as needed
  // For now, we'll handle the most common case (Germany)

  // Validate the cleaned number still has enough digits
  const finalDigits = cleaned.replace(/[^0-9]/g, '');
  if (finalDigits.length < 7 || finalDigits.length > 15) return null;

  // Add extension back if present
  if (extension) {
    cleaned += 'x' + extension;
  }

  return cleaned;
}

/**
 * Validate and sanitize query parameters
 */
function validateQueryParams(searchParams: URLSearchParams): { key: string; secret: string } | null {
  const key = searchParams.get('key');
  const secret = searchParams.get('secret');

  if (!key || !secret) {
    return null;
  }

  // Sanitize inputs to prevent injection attacks
  const sanitizedKey = key.replace(/[^a-zA-Z0-9\-_.~]/g, '').substring(0, 255);
  const sanitizedSecret = secret.replace(/[^a-zA-Z0-9\-_.~]/g, '').substring(0, 255);

  if (!sanitizedKey || !sanitizedSecret) {
    return null;
  }

  return { key: sanitizedKey, secret: sanitizedSecret };
}

/**
 * Authenticate using key and secret
 */
async function authenticateKeySecret(key: string, secret: string): Promise<{ userId: string; keyPasswordId: string } | null> {
  try {
    const db = await getDb();

    const result = await db
      .select({
        id: phonebookKeyPasswords.id,
        userId: phonebookKeyPasswords.userId,
      })
      .from(phonebookKeyPasswords)
      .where(
        and(
          eq(phonebookKeyPasswords.key, key),
          eq(phonebookKeyPasswords.secret, secret)
        )
      )
      .limit(1);

      console.log({result});

    if (result.length === 0) {
      return null;
    }

    return {
      userId: result[0].userId,
      keyPasswordId: result[0].id,
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

/**
 * Get contacts for user and format for Yealink
 */
async function getUserContactsForYealink(userId: string): Promise<YealinkContact[]> {
  try {
    const db = await getDb();

    const userProfile = await db.select().from(user).where(eq(user.id, userId));
    let role = 'user';
    if (userProfile[0].role === 'admin') {
      role = 'admin';
    }
    let userContacts
    if (role === 'admin') {
      userContacts = await db
        .select({
          name: contacts.name,
          phone: contacts.phone,
        })
        .from(contacts);
    } else {
      userContacts = await db
        .select()
        .from(contacts)
        .where(eq(contacts.userId, userId));
    }
    if (!userContacts) {
      return [];
    }
    return userContacts
      .filter(contact => contact.phone && isValidPhoneNumber(contact.phone)) // Only include contacts with valid phone numbers
      .map(contact => {
        const formattedPhone = formatPhoneNumberForYealink(contact.phone!);
        if (!formattedPhone) return null;

        return {
          name: sanitizeXmlValue(contact.name),
          telephone: sanitizeXmlValue(formattedPhone),
        };
      })
      .filter((contact): contact is YealinkContact =>
        contact !== null && !!contact.name && !!contact.telephone
      ); // Ensure both name and phone exist and are valid
  } catch (error) {
    console.error('Error fetching contacts:', error);
    return [];
  }
}

/**
 * Generate Yealink XML directory
 * Uses the correct DirectoryEntry format as per Yealink documentation
 */
function generateYealinkXml(contacts: YealinkContact[]): string {
  const directoryEntries = contacts
    .map(contact =>
      `    <DirectoryEntry>\n        <Name>${contact.name}</Name>\n        <Telephone>${formatPhoneNumberForYealink(contact.telephone)}</Telephone>\n    </DirectoryEntry>`
    )
    .join('\n');

  return `<YealinkIPPhoneDirectory>
${directoryEntries}
</YealinkIPPhoneDirectory>`;
}

export async function GET(request: NextRequest) {
  const startTime = Date.now();
  let userId: string | undefined;
  let keyPasswordId: string | undefined;

  try {
    // Check User-Agent header
    const userAgent = request.headers.get('user-agent') || '';
    if (!userAgent.toLowerCase().includes('yealink')) {
      await logApiAccess('/api/yealink/directory', false, undefined, undefined, 'Invalid User-Agent');
      return NextResponse.json(
        { error: 'Unauthorized: Invalid Reqquest' },
        { status: 401 }
      );
    }

    // Validate and sanitize query parameters
    const { searchParams } = new URL(request.url);
    const credentials = validateQueryParams(searchParams);

    if (!credentials) {
      await logApiAccess('/api/yealink/directory', false, undefined, undefined, 'Missing or invalid credentials');
      return NextResponse.json(
        { error: 'Bad Request: Missing or invalid parameters' },
        { status: 400 }
      );
    }

    // Authenticate key and secret
    const auth = await authenticateKeySecret(credentials.key, credentials.secret);
    if (!auth) {
      await logApiAccess('/api/yealink/directory', false, undefined, undefined, 'Authentication failed');
      return NextResponse.json(
        { error: 'Unauthorized: Invalid Request' },
        { status: 401 }
      );
    }

    userId = auth.userId;
    keyPasswordId = auth.keyPasswordId;

    // Get user contacts
    const userContacts = await getUserContactsForYealink(userId);

    // Generate XML response
    const xmlContent = generateYealinkXml([...userContacts, { name: 'Dr. Jasmin Last', telephone: '+4917613355888' }, { name: 'Dr. Jasmin Last', telephone: '+491743217968' }]);

    // Log successful API access
    const responseTime = Date.now() - startTime;
    await logApiAccess(
      '/api/yealink/directory',
      true,
      userId,
      keyPasswordId,
      `Returned ${userContacts.length} contacts in ${responseTime}ms`
    );

    // Return XML with proper content type
    return new NextResponse(xmlContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    console.error('Yealink directory API error:', error);

    // Log error
    await logApiAccess(
      '/api/yealink/directory',
      false,
      userId,
      keyPasswordId,
      `Internal server error: ${error}`
    );

    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// Only allow GET requests
export async function POST() {
  return NextResponse.json(
    { error: 'Method Not Allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method Not Allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method Not Allowed' },
    { status: 405 }
  );
}
