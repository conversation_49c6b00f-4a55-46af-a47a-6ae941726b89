"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

interface CreateKeyButtonProps {
  variant?: "default" | "outline" | "ghost"
  size?: "default" | "sm" | "lg"
  className?: string
  children?: React.ReactNode
}

export function CreateKeyButton({
  variant = "default",
  size = "default",
  className,
  children
}: CreateKeyButtonProps) {
  const [isCreating, setIsCreating] = useState(false)
  const router = useRouter()
  const { toast } = useToast()

  const handleCreateKey = async () => {
    setIsCreating(true)
    try {
      const response = await fetch('/api/phonebook-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        await response.json()
        toast({
          title: "API Key Created",
          description: "Your new API key has been created successfully.",
        })

        // Refresh the page to show the new key
        router.refresh()
      } else {
        const errorData = await response.json() as { error?: string }
        toast({
          title: "Creation Failed",
          description: errorData.error || "Failed to create API key. Please try again.",
          variant: "destructive",
        })
      }
    } catch {
      toast({
        title: "Creation Failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleCreateKey}
      disabled={isCreating}
    >
      <Plus className="w-4 h-4 mr-2" />
      {isCreating ? "Creating..." : (children || "Create API Key")}
    </Button>
  )
}
