# Phonebook Project Context

## Project Overview
This is a phonebook application with Yealink IP phone integration. The project uses:
- Next.js 15.3.5 with TypeScript
- pnpm as package manager
- Drizzle ORM for database management
- Better Auth for authentication
- Cloudflare deployment

## Recent Changes

### 2024-12-19: Phone Number Formatting Update
- **File Modified**: `app/api/yealink/directory/route.ts`
- **Function**: `formatPhoneNumberForYealink`
- **Change**: Modified to convert international numbers to local format
- **Examples**: 
  - `+491743217968` → `01743217968` (international with +)
  - `491743217968` → `01743217968` (international without +)
  - `01743217968` → `01743217968` (already local, unchanged)
- **Implementation**: 
  - Handles international numbers with or without + prefix
  - Removes country code and adds leading 0 for local format (Germany +49)
  - Validates number length to avoid false positives
  - Maintains existing validation and extension handling
  - Preserves extension format (e.g., `x123`)

## Current Functionality
- Yealink IP phone directory API endpoint
- Phone number validation and formatting
- International to local number conversion
- Extension support (x, ext, extension)
- XML injection protection
- GDPR compliant logging

## Package Manager
Using `pnpm` as evidenced by `pnpm-lock.yaml` file

## Code Standards
- JSDoc comments for all functions and interfaces
- TypeScript strict mode
- ESLint configuration
- Clean code practices with proper cleanup of old code 