import { NextRequest, NextResponse } from 'next/server';
import { getAuth, auth } from '@/lib/auth/config';
import { headers } from 'next/headers';

export type AuthenticatedHandler<T = Record<string, string | string[]>> = (
  request: NextRequest,
  context: {
    user: typeof auth.$Infer.Session.user;
    session: typeof auth.$Infer.Session.session;
  },
  routeContext?: { params: T }
) => Promise<NextResponse> | NextResponse;

export type AuthenticatedHandlerTwoParams = (
  request: NextRequest,
  context: {
    user: typeof auth.$Infer.Session.user;
    session: typeof auth.$Infer.Session.session;
  }
) => Promise<NextResponse> | NextResponse;

export type UnauthenticatedHandler<T = Record<string, string | string[]>> = (
  request: NextRequest,
  routeContext?: { params: T }
) => Promise<NextResponse> | NextResponse;

/**
 * Higher-order function that wraps API route handlers with authentication
 * 
 * @param handler - The authenticated route handler function
 * @returns A Next.js API route handler with authentication
 * 
 * @example
 * ```typescript
 * // Simple route without parameters
 * export const GET = withAuth(async (request, { user }) => {
 *   // user is guaranteed to be authenticated here
 *   const userContacts = await getUserContacts(user.id);
 *   return NextResponse.json(userContacts);
 * });
 *
 * // Route with parameters (e.g., /api/users/[id]/route.ts)
 * export const GET = withAuth(async (request, { user }, { params }) => {
 *   const { id } = await params; // params is a Promise in App Router
 *   const userData = await getUserById(id);
 *   return NextResponse.json(userData);
 * });
 * ```
 */
export function withAuth<T = Record<string, string | string[]>>(
  handler: AuthenticatedHandler<T>
): UnauthenticatedHandler<T> {
  return async (request: NextRequest, routeContext?: { params: T }) => {
    try {
      const auth = await getAuth();
      
      // For GET requests without body, use headers() from next/headers
      // For other requests, use request.headers directly
      const requestHeaders = request.method === 'GET' 
        ? await headers()
        : request.headers;

      const session = await auth.api.getSession({
        headers: requestHeaders,
      });

      if (!session?.user?.id) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Create session with role field
      const sessionWithRole = {
        user: {
          ...session.user,
          role: (session.user as { role?: string }).role || 'user',
        },
        session: session.session,
      };

      // Check if handler expects 3 parameters or 2
      if (handler.length === 2) {
        return await (handler as AuthenticatedHandlerTwoParams)(request, sessionWithRole);
      } else {
        return await handler(request, sessionWithRole, routeContext);
      }
    } catch (error) {
      console.error('Authentication error:', error);
      return NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Utility function to get the current authenticated user from a request
 * This is useful for middleware or other contexts where you need to check auth
 * but don't want to wrap the entire handler
 * 
 * @param request - The Next.js request object
 * @returns The authenticated user or null if not authenticated
 */
export async function getCurrentUser(request: NextRequest) {
  try {
    const auth = await getAuth();
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    if (!session?.user?.id) {
      return null;
    }

    return session.user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Alternative withAuth that allows for custom error responses
 * 
 * @param handler - The authenticated route handler function
 * @param options - Configuration options
 * @returns A Next.js API route handler with authentication
 */
export function withAuthCustom<T = Record<string, string | string[]>>(
  handler: AuthenticatedHandler<T>,
  options?: {
    onUnauthorized?: () => NextResponse;
    onError?: (error: unknown) => NextResponse;
  }
): UnauthenticatedHandler<T> {
  return async (request: NextRequest, routeContext?: { params: T }) => {
    try {
      const auth = await getAuth();
      
      const requestHeaders = request.method === 'GET' 
        ? await headers()
        : request.headers;

      const session = await auth.api.getSession({
        headers: requestHeaders,
      });

      if (!session?.user?.id) {
        return options?.onUnauthorized?.() || NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Create session with role field
      const sessionWithRole = {
        user: {
          ...session.user,
          role: (session.user as { role?: string }).role || 'user',
        },
        session: session.session,
      };

      console.log('Session with role:', sessionWithRole);

      // Check if handler expects 3 parameters or 2
      if (handler.length === 2) {
        return await (handler as AuthenticatedHandlerTwoParams)(request, sessionWithRole);
      } else {
        return await handler(request, sessionWithRole, routeContext);
      }
    } catch (error) {
      console.error('Authentication error:', error);
      return options?.onError?.(error) || NextResponse.json(
        { error: 'Internal Server Error' },
        { status: 500 }
      );
    }
  };
}
